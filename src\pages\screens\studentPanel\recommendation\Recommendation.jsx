'use client';

import { useEffect, useState } from 'react';
import {
  <PERSON>,
  Clock,
  CheckCircle2,
  Target,
  Trophy,
  TrendingUp,
  Zap,
  Award,
  Activity,
  ChevronRight,
  Star,
  Timer,
  BarChart3,
  X,
  Check,
  Lightbulb
} from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import {
  setRecommendationData,
  setRecommendationPendingData,
  useLazyGetRecommendationsByIdQuery,
  useLazyGetRecommendationsQuery
} from './recommendation.slice';

const Recommendation = () => {
  const [screenHeight, setScreenHeight] = useState(0);
  const [activeTab, setActiveTab] = useState('pending');
  const [popUp, setPopUp] = useState(false);
  const [selectedAnswers, setSelectedAnswers] = useState({});
  const [quizSubmitted, setQuizSubmitted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [showResults, setShowResults] = useState(false);

  const dispatch = useDispatch();

  // Mock Redux data - replace with actual Redux selectors
  const [getRecommendations] = useLazyGetRecommendationsQuery();
  const [getRecommendationsById] = useLazyGetRecommendationsByIdQuery();
  const recommendationData = useSelector((state) => state.recommendation.recommendationData);
  const recommendationPendingData = useSelector(
    (state) => state.recommendation.recommendationPendingData
  );

  useEffect(() => {
    setScreenHeight(window.innerHeight);
    const handleResize = () => setScreenHeight(window.innerHeight);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    handleGetRecommendations();
  }, []);

  const handleGetRecommendations = async () => {
    try {
      const recommendations = await getRecommendations({
        user_id: sessionStorage.userId
      }).unwrap();
      dispatch(setRecommendationData(recommendations.data));
      console.log('Fetched recommendations:', recommendations);
    } catch (error) {
      console.error('Error fetching recommendations:', error);
    }
  };

  const handleGetRecommendationsById = async (missionId) => {
    try {
      const recommendations = await getRecommendationsById({
        mission_id: missionId,
        user_id: sessionStorage.userId
      }).unwrap();
      dispatch(setRecommendationPendingData(recommendations.data));
      setPopUp(true);
      console.log('Fetched recommendations by ID:', recommendations);
    } catch (error) {
      console.error('Error fetching recommendations by ID:', error);
    }
  };

  const handleAnswerSelect = (questionIndex, answer) => {
    setSelectedAnswers((prev) => ({
      ...prev,
      [questionIndex]: answer
    }));
  };

  const handleQuizSubmit = () => {
    setQuizSubmitted(true);
    setShowResults(true);
  };

  const calculateScore = () => {
    let correct = 0;
    recommendationPendingData?.quiz_questions?.forEach((question, index) => {
      if (selectedAnswers[index] === question.correct_answer) {
        correct++;
      }
    });
    return Math.round((correct / recommendationPendingData?.quiz_questions?.length) * 100);
  };

  const getDifficultyColor = (difficulty) => {
    switch (difficulty?.toLowerCase()) {
      case 'beginner':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'advanced':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'expert':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const MissionCard = ({ mission, index }) => (
    <div
      className="group hover:shadow-lg transition-all duration-300 cursor-pointer border-l-4 border-l-blue-500 hover:border-l-blue-600 bg-white border border-gray-200 rounded-xl shadow-sm"
      onClick={() => handleGetRecommendationsById(mission.mission_id)}
    >
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-semibold text-lg text-gray-900 group-hover:text-blue-600 transition-colors">
                {mission.title}
              </h3>
              {mission.attempt_id > 0 && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
                  Retry {mission.attempt_id}
                </span>
              )}
            </div>
            <p className="text-sm text-gray-600 mb-3">
              {mission.subject_name} • {mission.chapter_name} • {mission.sub_topic_name}
            </p>
          </div>
          <ChevronRight className="w-5 h-5 text-gray-400 group-hover:text-blue-500 transition-colors" />
        </div>

        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getDifficultyColor(mission.difficulty)}`}
            >
              {mission.difficulty}
            </span>
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <Timer className="w-4 h-4" />
              {mission.estimated_time}
            </div>
          </div>
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <TrendingUp className="w-4 h-4" />
            {mission.completion_rate}% completion rate
          </div>
        </div>

        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${mission.completion_rate}%` }}
          />
        </div>
      </div>
    </div>
  );

  const QuizQuestion = ({ question, index, isActive }) => {
    const isAnswered = selectedAnswers[index];
    const isCorrect = quizSubmitted && selectedAnswers[index] === question.correct_answer;
    const isWrong =
      quizSubmitted && selectedAnswers[index] && selectedAnswers[index] !== question.correct_answer;

    return (
      <div className={`transition-all duration-300 ${isActive ? 'opacity-100' : 'opacity-50'}`}>
        <div className="mb-6">
          <div className="flex items-start gap-3 mb-4">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                isCorrect
                  ? 'bg-green-100 text-green-600'
                  : isWrong
                    ? 'bg-red-100 text-red-600'
                    : 'bg-blue-100 text-blue-600'
              }`}
            >
              {index + 1}
            </div>
            <h3 className="text-lg font-medium text-gray-900 flex-1">{question.question_text}</h3>
            {quizSubmitted && (
              <div className="flex items-center gap-1">
                {isCorrect ? (
                  <Check className="w-5 h-5 text-green-600" />
                ) : (
                  <X className="w-5 h-5 text-red-600" />
                )}
              </div>
            )}
          </div>

          <div className="space-y-3">
            {Object.entries(question.options).map(([optionKey, optionValue]) => {
              const isSelected = selectedAnswers[index] === optionKey;
              const isCorrectOption = question.correct_answer === optionKey;

              return (
                <div
                  key={optionKey}
                  className={`flex items-center space-x-3 p-4 rounded-lg border transition-all ${
                    quizSubmitted
                      ? isCorrectOption
                        ? 'border-green-200 bg-green-50'
                        : isSelected && !isCorrectOption
                          ? 'border-red-200 bg-red-50'
                          : 'border-gray-200'
                      : isSelected
                        ? 'border-blue-200 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    id={`${index}-${optionKey}`}
                    name={`question-${index}`}
                    value={optionKey}
                    checked={selectedAnswers[index] === optionKey}
                    onChange={() => handleAnswerSelect(index, optionKey)}
                    disabled={quizSubmitted}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 focus:ring-2"
                  />
                  <label htmlFor={`${index}-${optionKey}`} className="flex-1 cursor-pointer">
                    <span className="font-medium">{optionKey}:</span> {optionValue}
                  </label>
                  {quizSubmitted && isCorrectOption && <Check className="w-4 h-4 text-green-600" />}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Enhanced Quiz Dialog */}
      {popUp && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b">
              <h2 className="text-2xl font-bold text-gray-900">
                {recommendationPendingData?.title}
              </h2>
              <button
                onClick={() => setPopUp(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <div className="flex-1 overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6 h-full">
                {/* Video and Content Section */}
                <div className="space-y-6">
                  <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
                    <div className="p-4">
                      <div className="aspect-video rounded-lg overflow-hidden mb-4">
                        <iframe
                          width="100%"
                          height="100%"
                          className="rounded-lg"
                          src="https://www.youtube.com/embed/3f_eisNPpnc?si=Hl7b15-NXN-zE88D"
                          title="YouTube video player"
                          frameBorder="0"
                          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                          referrerPolicy="strict-origin-when-cross-origin"
                          allowFullScreen
                        />
                      </div>
                      <div className="flex items-center gap-2 mb-3">
                        <Lightbulb className="w-5 h-5 text-yellow-500" />
                        <h3 className="font-semibold text-gray-900">Learning Content</h3>
                      </div>
                      <div className="h-32 overflow-y-auto">
                        <p className="text-gray-700 text-sm leading-relaxed">
                          {recommendationPendingData?.explanation_markdown}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Quiz Section */}
                <div className="space-y-6">
                  <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
                    <div className="p-6 border-b border-gray-200">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-2">
                          <Target className="w-5 h-5 text-blue-600" />
                          <h3 className="text-lg font-semibold text-gray-900">Knowledge Check</h3>
                        </div>
                        {!showResults && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
                            {Object.keys(selectedAnswers).length} /{' '}
                            {recommendationPendingData?.quiz_questions?.length} answered
                          </span>
                        )}
                      </div>
                      {!showResults && (
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{
                              width: `${
                                (Object.keys(selectedAnswers).length /
                                  recommendationPendingData?.quiz_questions?.length) *
                                100
                              }%`
                            }}
                          />
                        </div>
                      )}
                    </div>
                    <div className="p-6">
                      <div className="h-96 overflow-y-auto">
                        <div className="space-y-6">
                          {recommendationPendingData?.quiz_questions?.map((question, index) => (
                            <QuizQuestion
                              key={index}
                              question={question}
                              index={index}
                              isActive={true}
                            />
                          ))}
                        </div>
                      </div>

                      <div className="border-t border-gray-200 my-6"></div>

                      <div className="flex items-center justify-between">
                        {showResults ? (
                          <div className="flex items-center gap-4">
                            <div className="flex items-center gap-2">
                              <Trophy className="w-5 h-5 text-yellow-500" />
                              <span className="font-semibold">Score: {calculateScore()}%</span>
                            </div>
                            <span
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                calculateScore() >= 70
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }`}
                            >
                              {calculateScore() >= 70 ? 'Passed' : 'Needs Review'}
                            </span>
                          </div>
                        ) : (
                          <div className="text-sm text-gray-600">
                            Answer all questions to submit
                          </div>
                        )}

                        {!quizSubmitted && (
                          <button
                            onClick={handleQuizSubmit}
                            disabled={
                              Object.keys(selectedAnswers).length !==
                              recommendationPendingData?.quiz_questions?.length
                            }
                            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          >
                            Submit Quiz
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Header */}
      <header className="bg-white/95 backdrop-blur-md border-b border-slate-200 px-6 py-4 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <Brain className="w-7 h-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  Mission Control
                </h1>
                <p className="text-sm text-gray-600">AI-Powered Learning Platform</p>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Activity className="w-4 h-4" />
              <span>12 Active Missions</span>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Trophy className="w-4 h-4" />
              <span>Level 8</span>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto p-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="space-y-6">
              {/* Custom Tabs */}
              <div className="bg-gray-100 p-1 rounded-lg grid grid-cols-2">
                <button
                  onClick={() => setActiveTab('pending')}
                  className={`flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all ${
                    activeTab === 'pending'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <Clock className="w-4 h-4" />
                  Pending Missions
                </button>
                <button
                  onClick={() => setActiveTab('completed')}
                  className={`flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all ${
                    activeTab === 'completed'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <CheckCircle2 className="w-4 h-4" />
                  Completed Missions
                </button>
              </div>

              {/* Tab Content */}
              {activeTab === 'pending' && (
                <div className="space-y-4">
                  {recommendationData?.map((mission, index) => (
                    <MissionCard key={mission.mission_id} mission={mission} index={index} />
                  ))}
                </div>
              )}

              {activeTab === 'completed' && (
                <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-8 text-center">
                  <Trophy className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    No completed missions yet
                  </h3>
                  <p className="text-gray-600">Complete your first mission to see it here!</p>
                </div>
              )}
            </div>
          </div>

          {/* Enhanced Sidebar */}
          <div className="space-y-6">
            <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  <h3 className="text-lg font-semibold text-gray-900">Performance Overview</h3>
                </div>
              </div>
              <div className="p-6 space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Overall Progress</span>
                    <span className="font-medium">68%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: '68%' }}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Streak</span>
                    <span className="font-medium">7 days</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: '70%' }}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">24</div>
                    <div className="text-xs text-gray-600">Missions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">18</div>
                    <div className="text-xs text-gray-600">Completed</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center gap-2">
                  <Star className="w-5 h-5" />
                  <h3 className="text-lg font-semibold text-gray-900">Achievements</h3>
                </div>
              </div>
              <div className="p-6 space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                    <Award className="w-4 h-4 text-yellow-600" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">Quick Learner</div>
                    <div className="text-xs text-gray-600">Complete 5 missions in a day</div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <Zap className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-medium text-sm">Speed Runner</div>
                    <div className="text-xs text-gray-600">Complete mission under time limit</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Recommendation;
